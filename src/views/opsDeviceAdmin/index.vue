<template>
  <div class="opsDeviceAdmin">
    <div class="table-wrapper">
      <div class="table-header ops-device-admin-header">
        <div class="table-title">
          <el-popover
            placement="bottom-start"
            width="500"
            trigger="click"
            ref="filterPopover"
            @after-leave="clickHidePopover"
          >
            <!-- 动态筛选表单 -->
            <el-form
              ref="formPopover"
              :model="searchFormSetting"
              inline
              label-width="130px"
              :style="formStyle"
              size="mini"
            >
              <el-form-item
                v-for="(item, key) in searchFormSetting"
                :key="key"
                :label="item.label"
                :prop="key + '.value'"
                :label-width="item.labelWidth"
              >
                <!-- @change="searchHandle(item)" -->
                <el-select
                  v-if="item.type === 'select'"
                  :class="['search-item-w', 'ps-select', item.fixedWidth ? 'w-250' : '']"
                  popper-class="ps-popper-select"
                  v-model="item.value"
                  :placeholder="item.placeholder"
                  :multiple="item.multiple"
                  :collapse-tags="item.collapseTags"
                  :clearable="item.clearable"
                  :filterable="item.filterable"
                  :disabled="item.disabled"
                  :style="{ width: item.maxWidth ? item.maxWidth : '250px' }"
                >
                  <el-option
                    v-for="(option, i) in item.dataList"
                    :key="i"
                    :label="item.listNameKey ? option[item.listNameKey] : option.label"
                    :value="item.listValueKey ? option[item.listValueKey] : option.value"
                    :disabled="option.disabled"
                  ></el-option>
                </el-select>
                <el-date-picker
                  v-if="item.type === 'daterange'"
                  v-model="item.value"
                  type="daterange"
                  :clearable="item.clearable"
                  :format="item.format ? item.format : 'yyyy-MM-dd'"
                  :value-format="item.format ? item.format : 'yyyy-MM-dd'"
                  align="left"
                  unlink-panels
                  range-separator="⇀"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :picker-options="item.pickerOptions ? item.pickerOptions : pickerOptions"
                  class="ps-picker"
                  popper-class="ps-poper-picker"
                  :style="{ width: item.maxWidth ? item.maxWidth : '300px' }"
                ></el-date-picker>
                <!-- </div> -->
                <el-input
                  v-if="item.type === 'input'"
                  class="search-item-w ps-input"
                  v-model.trim="item.value"
                  :placeholder="item.placeholder"
                  :clearable="item.clearable"
                  :maxlength="item.maxlength"
                  :style="{ width: item.maxWidth ? item.maxWidth : '300px' }"
                ></el-input>
                <el-radio-group
                  v-if="item.type === 'radio'"
                  v-model="item.value"
                  :style="{ width: item.maxWidth ? item.maxWidth : '300px' }"
                >
                  <el-radio v-for="radio in item.dataList" :key="radio.value" :label="radio.value" :name="radio.label">
                    {{ radio.label }}
                  </el-radio>
                </el-radio-group>
                <el-checkbox-group
                  v-if="item.type === 'checkboxGroup'"
                  v-model="item.value"
                  :style="{ width: item.maxWidth ? item.maxWidth : '300px' }"
                >
                  <el-checkbox
                    class="ps-checkbox"
                    v-for="(checkbox, i) in item.dataList"
                    :key="i"
                    :label="item.listValueKey ? checkbox[item.listValueKey] : checkbox.value"
                    :name="item.listName"
                  >
                    {{ item.listNameKey ? checkbox[item.listNameKey] : checkbox.label }}
                  </el-checkbox>
                </el-checkbox-group>
                <el-radio-group
                  v-if="item.type === 'radioGroup'"
                  v-model="item.value"
                  :style="{ width: item.maxWidth ? item.maxWidth : '300px' }"
                >
                  <el-radio
                    class="ps-radio"
                    v-for="(radio, i) in item.dataList"
                    :key="i"
                    :label="item.listValueKey ? radio[item.listValueKey] : radio.value"
                    :name="item.listName"
                  >
                    {{ item.listNameKey ? radio[item.listNameKey] : radio.label }}
                  </el-radio>
                </el-radio-group>
                <organization-select
                  v-if="item.type === 'organizationSelect'"
                  :style="{ width: item.maxWidth ? item.maxWidth : '300px' }"
                  class="search-item-w ps-input"
                  v-model="item.value"
                  :placeholder="item.placeholder"
                  :clearable="item.clearable"
                  :multiple="item.multiple"
                  :checkStrictly="item.checkStrictly"
                  :isLazy="item.isLazy"
                  :role="item.role"
                  :disabled="item.disabled"
                  :collapseTags="item.collapseTags"
                  :append-to-body="true"
                  :dataList="item.dataList"
                  :filterable="item.filterable"
                  :orgs="item.orgs"
                  size="mini"
                  @change="searchHandle"
                ></organization-select>
              </el-form-item>
            </el-form>
            <!-- 底部按钮 -->
            <div style="text-align: right; padding-top: 20px; border-top: 1px solid #ebeef5">
              <el-button type="primary" size="small" @click="applyFilter">确定</el-button>
              <el-button size="small" @click="applyFilter">取消</el-button>
              <el-button size="small" @click="resetFilter">重置</el-button>

              <!-- 展开和收起按钮 -->
              <el-button v-if="shouldShowExpandButton" size="small" type="text" @click="toggleCollapse">
                {{ isCollapse ? '收起' : '展开' }}
                <i v-if="isCollapse" class="el-icon-caret-top el-icon--right"></i>
                <i v-else class="el-icon-caret-bottom el-icon--right"></i>
              </el-button>
            </div>
            <el-button size="small" type="primary" style="width: 80px" slot="reference">
              筛选
              <i class="el-icon-caret-bottom el-icon--right"></i>
            </el-button>
          </el-popover>
        </div>
        <div class="align-r">
          <el-select v-model="offlineStatus" placeholder="请选择" filterable clearable :style="{ width: '150px' }">
            <!-- 选中离线 当前筛选离线  所有设备离线-->
            <el-option :key="'选中离线'" :label="'选中离线'" :value="'offline'" />
            <el-option :key="'当前筛选离线'" :label="'当前筛选离线'" :value="'currentOffline'" />
            <el-option :key="'所有设备离线'" :label="'所有设备离线'" :value="'allOffline'" />
          </el-select>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
          row-key="id"
        >
          <table-column v-for="(item, index) in tableSetting" :key="index" :col="item">
            <!-- 适用分组 -->
            <template #user_group="{ row }">
              <span class="ps-i pointer" @click="openDialog('group', row)">查看</span>
            </template>
            <template #qrcode="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="openDialog('code', row)">查看</el-button>
            </template>
            <template #using="{ row }">
              <el-switch v-model="row.using" @change="changeStatusModify(row)" />
            </template>
            <template #activation_status="{ row }">
              <span>{{ row.activation_status ? '已激活' : '未激活' }}</span>
            </template>
            <template #using_status="{ row }">
              <span :style="{ color: row.using ? '#56ba58' : 'red' }">{{ row.using ? '在线' : '离线' }}</span>
            </template>
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right">
          <pagination
            :onPaginationChange="onPaginationChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :layout="'total, prev, pager, next,sizes, jumper'"
            :total="totalCount"
          ></pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <custom-drawer
      v-if="dialogVisible"
      :show.sync="dialogVisible"
      :title="dialogTitle"
      size="800px"
      @close="handleDialogClose"
      @cancel="handleDialogCancel"
      @confirm="clickConfirmHandle"
    >
      <div class="p-20">
        <div class="code" v-if="dialogType === 'code'">
          <qrcode
            :value="dialogData.device_type === 'QCG' ? dialogData.cupboard_json : dialogData.activation_code"
            :options="{ width: 280 }"
            :margin="10"
            alt
            class="face-img"
          />
          <div class="code-info-box">
            <p>设备名：{{ dialogData.device_name }}</p>
            <p>设备类型：{{ dialogData.device_type_alias }}</p>
            <p>所属组织：{{ dialogData.consumer_name }}</p>
          </div>
        </div>
        <div v-if="dialogType === 'group'">
          <el-form
            :model="deviceForm"
            @submit.native.prevent
            status-icon
            ref="deviceForm"
            :rules="deviceFormRules"
            inline
            class="dialog-form"
            v-loading="isLoading"
          >
            <el-form-item label="适用分组" label-width="90px">
              <div>
                <el-form-item label="">
                  <el-radio-group v-model="deviceForm.groupType">
                    <el-radio label="all">按天</el-radio>
                    <el-radio label="meal">按餐</el-radio>
                  </el-radio-group>
                </el-form-item>

                <div v-if="deviceForm.groupType === 'meal'" style="width: 400px">
                  <el-form-item
                    v-for="meal in mealList"
                    :key="meal.value"
                    :label="meal.label"
                    :prop="'allowUserGroup.' + meal.value"
                    class="text-align-left"
                    label-width="80px"
                  >
                    <user-group-select
                      class="search-item-w ps-input w-300"
                      size="mini"
                      v-model="deviceForm.allowUserGroup[meal.value]"
                      :multiple="true"
                      collapseTags
                      clearable
                      placeholder="请下拉选择"
                      :option-data="groupList"
                      :show-other="true"
                    ></user-group-select>
                  </el-form-item>
                </div>
                <div v-if="deviceForm.groupType === 'all'">
                  <el-form-item label="全天" prop="allowUserGroup.all" class="text-align-left">
                    <user-group-select
                      class="search-item-w ps-input w-300"
                      size="mini"
                      v-model="deviceForm.allowUserGroup.all"
                      :multiple="true"
                      collapseTags
                      clearable
                      placeholder="请下拉选择"
                      :option-data="groupList"
                    ></user-group-select>
                  </el-form-item>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div v-if="dialogType === 'effective'">
          <el-form
            :model="deviceForm"
            @submit.native.prevent
            status-icon
            ref="deviceForm"
            :rules="deviceFormRules"
            label-width="125px"
            class="dialog-form"
            v-loading="isLoading"
          >
            <el-form-item label="有效期：" prop="validityDate">
              <el-date-picker
                v-model="deviceForm.validityDate"
                type="daterange"
                align="left"
                unlink-panels
                range-separator="至"
                start-placeholder="生效时间"
                end-placeholder="失效时间"
                :picker-options="effectivePickerOptions"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                clearable
                class="ps-poper-picker"
              ></el-date-picker>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </custom-drawer>
  </div>
</template>

<script>
import OrganizationSelect from '@/components/OrganizationSelect'
import { deepClone, getTreeChildArr } from '@/utils'
import qrcode from '@chenfengyuan/vue-qrcode'
import UserGroupSelect from '@/components/UserGroupSelect'
export default {
  data() {
    return {
      // 筛选表单配置 - 按照您提供的格式
      searchFormSetting: {
        date_type: {
          type: 'select',
          value: 'create_time',
          label: '',
          labelWidth: '0px',
          maxWidth: '120px',
          dataList: [
            { label: '创建时间', value: 'create_time' }
            // { label: '开始日期', value: 'start_time' },
            // { label: '结束日期', value: 'end_time' }
          ]
        },
        select_time: {
          type: 'daterange',
          maxWidth: '300px',
          label: '',
          clearable: false,
          value: []
        },
        company_ids: {
          maxWidth: '300px',
          type: 'select',
          multiple: true,
          filterable: true,
          collapseTags: true,
          label: '所属项目',
          value: [],
          placeholder: '请选择项目点',
          listNameKey: 'name',
          listValueKey: 'id',
          dataList: []
        },
        organization_ids: {
          maxWidth: '300px',
          type: 'organizationSelect',
          multiple: true,
          checkStrictly: true,
          label: '消费点',
          value: [],
          placeholder: '请选择消费点',
          role: 'super',
          orgs: []
        },
        device_type: {
          type: 'select',
          label: '设备类型',
          value: '',
          placeholder: '请选择设备类型',
          listNameKey: 'name',
          listValueKey: 'key',
          dataList: []
        },
        device_name: {
          type: 'input',
          label: '设备名称',
          value: '',
          placeholder: '请输入设备名称',
          clearable: true
        },
        using: {
          type: 'radio',
          label: '使用状态',
          value: '',
          dataList: [
            { label: '全部', value: '' },
            { label: '启用', value: true },
            { label: '禁用', value: false }
          ]
        },
        activation_code: {
          type: 'input',
          label: '激活码',
          value: '',
          placeholder: '请输入激活码',
          clearable: true
        },
        activation_status: {
          type: 'radio',
          label: '激活状态',
          value: '',
          dataList: [
            { label: '全部', value: '' },
            { label: '已激活', value: true },
            { label: '未激活', value: false }
          ]
        },
        // code_valid_status: {
        //   type: 'radio',
        //   label: '激活码状态',
        //   value: '',
        //   dataList: [
        //     { label: '全部', value: '' },
        //     { label: '生效', value: 'valid' },
        //     { label: '失效', value: 'invalid' }
        //   ]
        // },
        device_mac: {
          type: 'input',
          label: 'Mac地址',
          value: '',
          placeholder: '请输入Mac地址',
          clearable: true
        },
        sn_code: {
          type: 'input',
          label: 'SN码',
          value: '',
          placeholder: '请输入SN码',
          clearable: true
        },
        version_number: {
          type: 'input',
          label: '设备版本号',
          value: '',
          placeholder: '请输入设备版本号',
          clearable: true
        },
        offline_face_activation_code: {
          type: 'input',
          label: '离线人脸激活码',
          value: '',
          placeholder: '请输入离线人脸激活码',
          clearable: true
        },
        is_about_to_expire: {
          type: 'checkboxGroup',
          label: '临近失效',
          value: [],
          placeholder: '请选择',
          dataList: [{ label: '是', value: true }]
        },
        sort_type: {
          type: 'radioGroup',
          label: '排序方式',
          value: 'desc',
          dataList: [
            { label: '升序', value: 'asc' },
            { label: '降序', value: 'desc' }
          ]
        }
      },
      pickerOptions: {
        // disabledDate(time) {
        //   return time.getTime() > Date.now();
        // },
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      offlineStatus: '',
      isLoading: false,
      dialogVisible: false,
      dialogType: '',
      dialogTitle: '',
      dialogData: {},

      // 展开收起相关
      isCollapse: false, // 是否展开状态
      formHeight: 0, // 表单实际高度

      tableSetting: [
        { label: '', key: 'selection', type: 'selection', reserveSelection: true },
        { label: '所属项目', key: 'company_name' },
        { label: '所属组织', key: 'consumer_name' },
        { label: '适用分组', key: 'user_group', type: 'slot', slotName: 'user_group' },
        { label: '设备名称', key: 'device_name' },
        { label: '设备类型', key: 'device_type_alias' },
        { label: '设备型号', key: 'device_model_alias' },
        { label: '设备ID', key: 'device_no' },
        { label: '版本号', key: 'version_number' },
        { label: 'SN码', key: 'serial_no' },
        { label: 'MAC地址', key: 'device_mac' },
        { label: '激活码', key: 'activation_code' },
        { label: '激活二维码', key: 'qrcode', type: 'slot', slotName: 'qrcode' },
        { label: '激活状态', key: 'activation_status', type: 'slot', slotName: 'activation_status' },
        { label: '有效期', key: 'activate_time' },
        { label: '使用状态', key: 'using', type: 'slot', slotName: 'using' },
        { label: '设备状态', key: 'using_status', type: 'slot', slotName: 'using_status' },
        { label: '离线人脸激活码', key: 'offline_face_activation_code', width: '150px' },
        { label: '创建时间', key: 'create_time' },
        { label: '操作人', key: 'operator_name' }
      ],
      tableData: [],
      orderIds: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,

      // 弹窗相关
      deviceForm: {
        groupType: 'all',
        allowUserGroup: {
          all: [],
          breakfast: [],
          lunch: [],
          afternoon: [],
          dinner: [],
          supper: [],
          morning: []
        },
        validityDate: []
      },
      deviceFormRules: {
        validityDate: [{ required: true, message: '请选择有效期', trigger: 'change' }]
      },
      groupList: [],
      mealList: [
        { label: '早餐', value: 'breakfast' },
        { label: '午餐', value: 'lunch' },
        { label: '下午茶', value: 'afternoon' },
        { label: '晚餐', value: 'dinner' },
        { label: '夜宵', value: 'supper' },
        { label: '凌晨餐', value: 'morning' }
      ],
      // 有效期选择器配置
      effectivePickerOptions: {
        shortcuts: [{
          text: '一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(end.getTime() + 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(end.getTime() + 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '一年',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            end.setTime(end.getTime() + 3600 * 1000 * 24 * 365);
            picker.$emit('pick', [start, end]);
          }
        }]
      }
    }
  },
  components: {
    OrganizationSelect,
    qrcode,
    UserGroupSelect
  },
  computed: {
    // 表单样式 - 根据是否展开和实际高度动态设置
    formStyle() {
      // 如果还没有计算高度，先不设置样式，避免闪烁
      if (this.formHeight === 0) {
        return {}
      }

      if (this.formHeight <= 600) {
        // 如果内容高度不超过570px，不设置固定高度
        return {}
      } else {
        // 如果内容高度超过570px，根据展开状态设置高度
        return {
          height: this.isCollapse ? 'auto' : '460px',
          overflow: 'hidden', // 不使用滚动条，直接隐藏超出部分
          transition: 'height 0.3s ease' // 添加平滑过渡效果
        }
      }
    },

    // 是否显示展开按钮 - 只有当内容高度超过570px时才显示
    shouldShowExpandButton() {
      return this.formHeight > 570
    }
  },
  watch: {
    'searchFormSetting.company_ids.value': function (val) {
      this.searchFormSetting.organization_ids.orgs = val
      let arr = []
      val.map(com => {
        arr = arr.concat(
          getTreeChildArr(this.searchFormSetting.company_ids.dataList, com, { key: 'id', childkey: 'children_list' })
        )
      })
      this.searchFormSetting.organization_ids.value.map(org => {
        if (arr.indexOf(org) === -1) {
          this.searchFormSetting.organization_ids.value.splice(
            this.searchFormSetting.organization_ids.value.indexOf(org),
            1
          )
        }
      })
    },
    // 监听表单配置变化，重新计算高度
    searchFormSetting: {
      handler() {
        this.$nextTick(() => {
          this.calculateFormHeight()
        })
      },
      deep: true
    }
  },
  mounted() {
    this.getDeviceDeviceList()
    this.getConsumeList()
    this.getDeviceType()
    // 修改分组弹窗
    this.userGroupList()
    // 预先计算表单高度，避免首次显示时闪烁
    this.preCalculateFormHeight()
  },
  methods: {
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key !== 'select_date') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_date = data[key].value[0]
            params.end_date = data[key].value[1]
          }
        }
      }
      if (params.company_ids && params.company_ids.length) {
        // 选择了项目点，做下处理
        if (!params.organization_ids) {
          // 只选了项目点，把所有项目点下面的组织id拿到
          params.organization_ids = []
          params.company_ids.map(item => {
            let arr = []
            arr = getTreeChildArr(this.searchFormSetting.company_ids.dataList, item, {
              key: 'id',
              childkey: 'children_list'
            })
            params.organization_ids = params.company_ids.concat(arr)
          })
        }
      }
      delete params.company_ids
      return params
    },
    // 获取设备类型
    async getDeviceType() {
      const res = await this.$apis.apiBackgroundDeviceDeviceDeviceTypePost()
      if (res.code === 0) {
        this.searchFormSetting.device_type.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    // 获取消费点信息
    async getConsumeList() {
      const res = await this.$apis.apiBackgroundAdminOrganizationTreeListPost()
      if (res.code === 0) {
        this.searchFormSetting.company_ids.dataList = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    clickHidePopover() {
      this.isCollapse = false
    },
    // 获取分组
    async userGroupList(organization) {
      const res = await this.$apis.apiCardServiceCardUserGroupListPost({
        status: 'enable',
        is_show_other: true,
        page: 1,
        page_size: 99999
      })
      if (res.code === 0) {
        this.groupList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },
    async getDeviceDeviceList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDeviceDeviceListPost({
        ...this.formatQueryParams(this.searchFormSetting),
        page: this.currentPage,
        page_size: this.pageSize
      })
      this.isLoading = false
      if (res.code === 0) {
        this.tableData = res.data.results
        // this.tableData.map(item => {
        //   if (item.activate_time.split(' ')[0]) {
        //     item.isEffective = true
        //     item.effective = item.activate_time.split(' ')[0]
        //     item.expiration = item.activate_time.split(' ')[3]
        //     let today = new Date()
        //     let expiration = new Date(item.activate_time.split(' ')[3])
        //     if (today - expiration > 0) {
        //       item.isOverLastDay = true
        //     } else {
        //       item.isOverLastDay = false
        //     }
        //   } else {
        //     item.isEffective = false
        //   }
        //   return item
        // })
        // this.allTableData = res.data.results
        // this.setSelCurrentPageData()
        this.totalCount = res.data.count
      } else {
        this.$message.error(res.msg)
      }
    },
    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      // this.getDraftBoxList()
    },
    // 修改状态
    changeStatusModify(data) {
      // 如果是开启使用状态，弹出有效期修改弹窗
      if (data.using) {
        this.openDialog('effective', data)
      } else {
        // 关闭状态直接调用接口
        this.$confirm('确定禁用该状态？', '提示', {
          confirmButtonText: this.$t('dialog.confirm_btn'),
          cancelButtonText: this.$t('dialog.cancel_btn'),
          closeOnClickModal: false,
          customClass: 'ps-confirm',
          cancelButtonClass: 'ps-cancel-btn',
          confirmButtonClass: 'ps-btn',
          center: true,
          beforeClose: async (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true
              this.isLoading = true
              const [err, res] = await this.$to(
                this.$apis.apiBackgroundAdminDeviceModifyPost({
                  device_no: data.device_no,
                  using: data.using
                })
              )
              this.isLoading = false
              instance.confirmButtonLoading = false
              done()
              if (err) {
                this.$message.error(err.message)
                return
              }
              if (res.code === 0) {
                done()
                this.$message.success(res.msg)
                this.getDeviceDeviceList()
              }
            } else {
              if (!instance.confirmButtonLoading) {
                done()
                data.using = !data.using
              }
            }
          }
        })
          .then(() => {})
          .catch(() => {})
      }
    },
    openDialog(type, data) {
      this.dialogData = data
      this.dialogType = type
      switch (type) {
        case 'code':
          this.dialogTitle = '查看二维码'
          break
        case 'group':
          if (this.dialogData.allow_user_group) {
            this.deviceForm.groupType = this.dialogData.allow_user_group_setting
            if (this.dialogData.allow_user_group_setting === 'all') {
              this.deviceForm.allowUserGroup.all = this.dialogData.allow_user_group.all
                ? this.dialogData.allow_user_group.all
                : []
            } else {
              this.mealList.map(meal => {
                this.deviceForm.allowUserGroup[meal.value] = this.dialogData.allow_user_group[meal.value]
              })
            }
          }
          this.dialogTitle = '修改分组'
          break
        case 'effective':
          this.dialogTitle = '修改有效期'
          this.deviceForm.validityDate = this.getNextYear()
          break
      }
      this.dialogVisible = true
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      console.log(val)
      var selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map(item => {
        selectListId.push(item.id)
      })
      this.orderIds = deepClone(selectListId)
    },
    // 搜索处理
    searchHandle(searchType) {
      console.log('搜索参数:', searchType)
      console.log('当前筛选条件:', this.searchFormSetting)
      // 这里可以调用API获取筛选后的数据
      // this.getTableData()
    },
    // 重置处理
    resetHandler() {
      console.log('重置筛选条件')
      // 重置后可以重新获取数据
      // this.getTableData()
    },
    // 应用筛选
    applyFilter() {
      console.log('应用筛选')
      // 获取当前表单的值
      const filterData = {}
      Object.keys(this.searchFormSetting).forEach(key => {
        filterData[key] = this.searchFormSetting[key].value
      })
      console.log('筛选数据:', filterData)

      // 这里可以调用API获取筛选后的数据
      // this.getTableData(filterData)

      // 关闭弹窗
      this.$refs.filterPopover.doClose()
    },
    // 重置筛选
    resetFilter() {
      // 调用search-form组件的重置方法
      if (this.$refs.searchRef) {
        this.$refs.searchRef.resetForm()
      }
      console.log('重置筛选')
    },
    // 导出功能
    gotoExport() {
      console.log('导出数据')
    },

    // 预先计算表单高度（在mounted时调用，避免闪烁）
    preCalculateFormHeight() {
      // 根据表单项数量预估高度
      const formItemCount = Object.keys(this.searchFormSetting).length
      // 每个表单项大约45px高度，加上边距
      const estimatedHeight = formItemCount * 45 + 60
      this.formHeight = estimatedHeight
      console.log('预估高度:', estimatedHeight, '表单项数量:', formItemCount)
    },

    // 计算表单实际高度（在popover显示时调用，获取精确高度）
    calculateFormHeight() {
      // 如果还没有预估高度，先使用预估值避免闪烁
      if (this.formHeight === 0) {
        this.preCalculateFormHeight()
      }

      // 使用较短的延迟获取精确高度
      this.$nextTick(() => {
        setTimeout(() => {
          const formEl = this.$refs.formPopover?.$el
          if (formEl) {
            // 临时移除高度限制以获取真实高度
            const originalHeight = formEl.style.height
            const originalOverflow = formEl.style.overflow

            formEl.style.height = 'auto'
            formEl.style.overflow = 'visible'

            // 获取实际高度
            let actualHeight = formEl.scrollHeight || formEl.offsetHeight || formEl.clientHeight

            // 如果获取到有效高度，更新formHeight
            if (actualHeight > 0) {
              console.log('精确高度:', actualHeight, '之前预估:', this.formHeight)
              this.formHeight = actualHeight
            }

            // 恢复原始样式
            formEl.style.height = originalHeight
            formEl.style.overflow = originalOverflow
          }
        }, 50) // 减少延迟时间到50ms
      })
    },

    // 切换展开收起状态
    toggleCollapse() {
      this.isCollapse = !this.isCollapse
    },
    clickConfirmHandle() {
      this.$refs.deviceForm.validate(valid => {
        if (valid) {
          let params
          switch (this.dialogType) {
            case 'group':
              params = {
                device_no: this.dialogData.device_no,
                // user_group_ids: this.deviceForm.group
                user_group_setting: this.deviceForm.groupType
              }
              if (this.deviceForm.groupType === 'all') {
                params.allow_user_group = {
                  all: this.deviceForm.allowUserGroup.all
                }
              } else {
                params.allow_user_group = {}
                this.mealList.map(meal => {
                  params.allow_user_group[meal.value] = this.deviceForm.allowUserGroup[meal.value]
                })
              }
              console.log(params)
              this.modifyDevice(params)
              break
            case 'effective':
              params = {
                device_no: this.dialogData.device_no,
                using: this.dialogData.using,
                time_range: {
                  start_date: this.deviceForm.validityDate[0],
                  end_date: this.deviceForm.validityDate[1]
                }
              }
              this.modifyDeviceEffective(params)
              break
          }
        }
      })
    },
    // 修改设备名、分组
    async modifyDevice(params) {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceDeviceModifyPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.getDeviceDeviceList()
        this.dialogVisible = false
      } else {
        this.$message.error(res.msg)
      }
    },
    // 修改设备有效期和状态
    async modifyDeviceEffective(params) {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundAdminDeviceModifyPost(params)
      this.isLoading = false
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.getDeviceDeviceList()
        this.dialogVisible = false
      } else {
        this.$message.error(res.msg)
        // 如果修改失败，恢复开关状态
        this.dialogData.using = !this.dialogData.using
      }
    },
    // 获取下一年的日期范围
    getNextYear() {
      let timeStamp = new Date().getTime() + 86400000 * (365)
      let startDate = [
        new Date().getFullYear(),
        (new Date().getMonth() + 1).toString().padStart(2, '0'),
        new Date()
          .getDate()
          .toString()
          .padStart(2, '0')
      ].join('-')

      let endDate = [
        new Date(timeStamp).getFullYear(),
        (new Date(timeStamp).getMonth() + 1).toString().padStart(2, '0'),
        new Date(timeStamp)
          .getDate()
          .toString()
          .padStart(2, '0')
      ].join('-')
      return [startDate, endDate]
    },
    // 处理弹窗关闭
    handleDialogClose() {
      if (this.dialogType === 'effective') {
        // 如果是有效期弹窗被关闭，恢复开关状态
        this.dialogData.using = !this.dialogData.using
      }
      this.dialogVisible = false
    },
    // 处理弹窗取消
    handleDialogCancel() {
      if (this.dialogType === 'effective') {
        // 如果是有效期弹窗被取消，恢复开关状态
        this.dialogData.using = !this.dialogData.using
      }
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.opsDeviceAdmin {
  padding: 20px;
  .table-wrapper {
    padding: 20px;
    background-color: #fff;
    border-radius: 20px;
  }
  .ops-device-admin-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }
}

// 筛选弹窗样式
:deep(.el-popover) {
  .search-form-wrapper {
    .search-header {
      margin-bottom: 10px;
    }

    .collapse-wrapper {
      max-height: 400px;
      overflow-y: auto;
    }

    .search-form-collapse {
      .el-form-item {
        margin-bottom: 0px;
        margin-right: 20px;

        .el-form-item__label {
          font-size: 13px;
        }
      }
    }
  }
}
</style>
